/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
  background-color: #f5f5f5;
}

.container {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.welcome-text {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 60rpx;
  text-align: center;
  font-weight: bold;
}

.template-btn {
  width: 400rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.template-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
}
