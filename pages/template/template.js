// template.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: null,
    
    // 输入框值
    inputValue: '',
    
    // 数据列表
    dataList: [
      {
        id: 1,
        title: '示例项目1',
        description: '这是第一个示例项目的描述'
      },
      {
        id: 2,
        title: '示例项目2',
        description: '这是第二个示例项目的描述'
      },
      {
        id: 3,
        title: '示例项目3',
        description: '这是第三个示例项目的描述'
      }
    ],
    
    // 加载状态
    loading: false,
    
    // 弹窗相关
    showModal: false,
    modalMessage: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('页面加载，参数：', options);
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('页面初次渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.refreshData();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
    this.loadMoreData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '分享标题',
      path: '/pages/template/template'
    };
  },

  /**
   * 初始化页面
   */
  initPage() {
    this.setData({
      loading: true
    });
    
    // 模拟获取用户信息
    setTimeout(() => {
      this.setData({
        userInfo: {
          id: '12345',
          nickName: '微信用户',
          avatarUrl: '/images/default-avatar.png'
        },
        loading: false
      });
    }, 1000);
  },

  /**
   * 主要操作按钮点击
   */
  onPrimaryAction() {
    console.log('主要操作被点击');
    wx.showToast({
      title: '主要操作执行',
      icon: 'success'
    });
  },

  /**
   * 次要操作按钮点击
   */
  onSecondaryAction() {
    console.log('次要操作被点击');
    this.setData({
      showModal: true,
      modalMessage: '确定要执行次要操作吗？'
    });
  },

  /**
   * 列表项点击
   */
  onItemTap(e) {
    const item = e.currentTarget.dataset.item;
    console.log('点击了列表项：', item);
    
    wx.showModal({
      title: item.title,
      content: item.description,
      showCancel: false
    });
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  /**
   * 提交按钮点击
   */
  onSubmit() {
    const { inputValue } = this.data;
    if (!inputValue.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    console.log('提交内容：', inputValue);
    wx.showToast({
      title: '提交成功',
      icon: 'success'
    });

    // 清空输入框
    this.setData({
      inputValue: ''
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    console.log('刷新数据');
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新完成',
        icon: 'success'
      });
    }, 1500);
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    console.log('加载更多数据');
    const { dataList } = this.data;
    const newItems = [
      {
        id: dataList.length + 1,
        title: `新项目${dataList.length + 1}`,
        description: `这是第${dataList.length + 1}个项目的描述`
      }
    ];

    this.setData({
      dataList: [...dataList, ...newItems]
    });
  },

  /**
   * 弹窗关闭
   */
  onModalClose() {
    this.setData({
      showModal: false
    });
  },

  /**
   * 弹窗取消
   */
  onModalCancel() {
    this.setData({
      showModal: false
    });
  },

  /**
   * 弹窗确认
   */
  onModalConfirm() {
    console.log('用户确认了操作');
    this.setData({
      showModal: false
    });
    
    wx.showToast({
      title: '操作完成',
      icon: 'success'
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});
