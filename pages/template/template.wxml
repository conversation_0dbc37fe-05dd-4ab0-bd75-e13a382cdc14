<!--template.wxml-->
<navigation-bar title="页面标题" back="{{true}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    
    <!-- 头部区域 -->
    <view class="header">
      <text class="title">欢迎使用模板页面</text>
      <text class="subtitle">这是一个微信小程序页面模板</text>
    </view>

    <!-- 用户信息卡片 -->
    <view class="user-card" wx:if="{{userInfo}}">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="user-info">
        <text class="nickname">{{userInfo.nickName}}</text>
        <text class="desc">用户ID: {{userInfo.id || '未登录'}}</text>
      </view>
    </view>

    <!-- 功能按钮区域 -->
    <view class="button-group">
      <button class="btn primary" bindtap="onPrimaryAction">主要操作</button>
      <button class="btn secondary" bindtap="onSecondaryAction">次要操作</button>
    </view>

    <!-- 列表区域 -->
    <view class="list-section">
      <text class="section-title">数据列表</text>
      <view class="list-container">
        <view class="list-item" wx:for="{{dataList}}" wx:key="id" bindtap="onItemTap" data-item="{{item}}">
          <view class="item-content">
            <text class="item-title">{{item.title}}</text>
            <text class="item-desc">{{item.description}}</text>
          </view>
          <view class="item-arrow">></view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{dataList.length === 0}}">
          <text class="empty-text">暂无数据</text>
        </view>
      </view>
    </view>

    <!-- 输入区域 -->
    <view class="input-section">
      <text class="section-title">输入示例</text>
      <input class="input-field" 
             placeholder="请输入内容" 
             value="{{inputValue}}" 
             bindinput="onInputChange"
             maxlength="100" />
      <button class="btn submit" bindtap="onSubmit" disabled="{{!inputValue}}">提交</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>

  </view>
</scroll-view>

<!-- 底部弹窗 -->
<view class="modal" wx:if="{{showModal}}" bindtap="onModalClose">
  <view class="modal-content" catchtap="stopPropagation">
    <text class="modal-title">提示</text>
    <text class="modal-message">{{modalMessage}}</text>
    <view class="modal-buttons">
      <button class="btn cancel" bindtap="onModalCancel">取消</button>
      <button class="btn confirm" bindtap="onModalConfirm">确认</button>
    </view>
  </view>
</view>
