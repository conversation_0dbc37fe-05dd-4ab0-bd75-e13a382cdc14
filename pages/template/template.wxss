/* template.wxss */

/* 全局容器 */
.scrollarea {
  height: 100vh;
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

/* 用户信息卡片 */
.user-card {
  background: white;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
}

.nickname {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn.secondary {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.btn.submit {
  background: #07c160;
  color: white;
  margin-top: 20rpx;
}

.btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.btn.confirm {
  background: #07c160;
  color: white;
}

.btn:disabled {
  background: #f5f5f5 !important;
  color: #ccc !important;
  border-color: #f5f5f5 !important;
}

/* 列表区域 */
.list-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.list-container {
  /* 列表容器样式 */
}

.list-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.item-content {
  flex: 1;
}

.item-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.item-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.item-arrow {
  color: #ccc;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.input-field:focus {
  border-color: #667eea;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #666;
  font-size: 28rpx;
}

/* 弹窗 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  width: 600rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
}

.modal-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.modal-message {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.modal-buttons {
  display: flex;
  gap: 20rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 15rpx;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .modal-content {
    width: 90%;
    margin: 0 5%;
  }
}
